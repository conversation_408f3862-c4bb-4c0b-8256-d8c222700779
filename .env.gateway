# ATMA Backend with API Gateway Environment Configuration

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=atma
DB_USER=postgres
DB_PASSWORD=atma_db_password_change_in_production

# Security Keys
JWT_SECRET=atma_jwt_secret_change_in_production_very_long_and_secure
INTERNAL_SERVICE_KEY=atma_internal_service_key_change_in_production_very_secure

# RabbitMQ Configuration
RABBITMQ_URL=amqp://admin:admin123@rabbitmq:5672
RABBITMQ_USER=admin
RABBITMQ_PASS=admin123

# API Gateway Configuration
GATEWAY_PORT=3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:3001

# Service Ports
AUTH_SERVICE_PORT=3001
ARCHIVE_SERVICE_PORT=3002
ASSESSMENT_SERVICE_PORT=3003
