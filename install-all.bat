@echo off
echo ========================================
echo Installing Dependencies for All ATMA Services
echo ========================================

echo.
echo Installing Auth Service dependencies...
cd auth-service
if not exist node_modules (
    echo Installing npm packages for Auth Service...
    call npm install
    if errorlevel 1 (
        echo Failed to install Auth Service dependencies
        pause
        exit /b 1
    )
    echo Auth Service dependencies installed successfully!
) else (
    echo Auth Service dependencies already installed.
)
cd ..

echo.
echo Installing Archive Service dependencies...
cd archive-service
if not exist node_modules (
    echo Installing npm packages for Archive Service...
    call npm install
    if errorlevel 1 (
        echo Failed to install Archive Service dependencies
        pause
        exit /b 1
    )
    echo Archive Service dependencies installed successfully!
) else (
    echo Archive Service dependencies already installed.
)
cd ..

echo.
echo Installing Assessment Service dependencies...
cd assessment-service
if not exist node_modules (
    echo Installing npm packages for Assessment Service...
    call npm install
    if errorlevel 1 (
        echo Failed to install Assessment Service dependencies
        pause
        exit /b 1
    )
    echo Assessment Service dependencies installed successfully!
) else (
    echo Assessment Service dependencies already installed.
)
cd ..

echo.
echo Installing API Gateway dependencies...
cd api-gateway
if not exist node_modules (
    echo Installing npm packages for API Gateway...
    call npm install
    if errorlevel 1 (
        echo Failed to install API Gateway dependencies
        pause
        exit /b 1
    )
    echo API Gateway dependencies installed successfully!
) else (
    echo API Gateway dependencies already installed.
)
cd ..

echo.
echo ========================================
echo All dependencies installed successfully!
echo ========================================
echo.
echo You can now start all services using:
echo   start-with-gateway.bat
echo.
echo Or start individual services:
echo   cd auth-service && npm start
echo   cd archive-service && npm start  
echo   cd assessment-service && npm start
echo   cd api-gateway && npm start
echo.
echo Press any key to exit...
pause >nul
